import React from 'react';
import './HelloWorld.css';

// Single Responsibility Principle (SRP)
// Este componente tiene una sola responsabilidad: presentar el mensaje
interface HelloWorldPresentationProps {
  message: string;
  isLoading: boolean;
}

export const HelloWorldPresentation: React.FC<HelloWorldPresentationProps> = ({
  message,
  isLoading
}) => {
  if (isLoading) {
    return (
      <div className="hello-world-container">
        <div className="loading">
          <div className="spinner"></div>
          <p>Cargando...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="hello-world-container">
      <h1 className="hello-world-title">{message}</h1>
      <p className="hello-world-subtitle">
        Proyecto React 19 con principios SOLID
      </p>
    </div>
  );
};
