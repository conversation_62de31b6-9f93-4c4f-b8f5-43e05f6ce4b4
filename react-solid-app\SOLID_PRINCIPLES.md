# Principios SOLID en React 19

Este proyecto demuestra la implementación de los principios SOLID en una aplicación React 19.

## Principios SOLID Implementados

### 1. Single Responsibility Principle (SRP)
**"Una clase debe tener una sola razón para cambiar"**

- **`GreetingService`**: Solo se encarga de generar saludos
- **`MessageFormatter`**: Solo se encarga de formatear mensajes
- **`HelloWorldPresentation`**: Solo se encarga de presentar la UI
- **`useHelloWorld`**: Solo maneja la lógica del estado del "Hola Mundo"

### 2. Open/Closed Principle (OCP)
**"Las entidades de software deben estar abiertas para extensión, pero cerradas para modificación"**

- Los servicios implementan interfaces, permitiendo crear nuevas implementaciones sin modificar el código existente
- Se pueden agregar nuevos formateadores o servicios de saludo sin cambiar el código del contenedor

### 3. Liskov Substitution Principle (LSP)
**"Los objetos de una superclase deben ser reemplazables con objetos de sus subclases"**

- Cualquier implementación de `IGreetingService` puede ser usada intercambiablemente
- Cualquier implementación de `IMessageFormatter` puede ser sustituida sin romper la funcionalidad

### 4. Interface Segregation Principle (ISP)
**"Los clientes no deben ser forzados a depender de interfaces que no usan"**

- `IGreetingService`: Interfaz específica para servicios de saludo
- `IMessageFormatter`: Interfaz específica para formateo de mensajes
- Cada interfaz es pequeña y específica para su propósito

### 5. Dependency Inversion Principle (DIP)
**"Depende de abstracciones, no de concreciones"**

- `useHelloWorld` depende de las interfaces `IGreetingService` e `IMessageFormatter`, no de implementaciones concretas
- El `HelloWorldContainer` inyecta las dependencias, permitiendo fácil testing y intercambio de implementaciones

## Estructura del Proyecto

```
src/
├── interfaces/           # Abstracciones (DIP, ISP)
│   ├── IGreetingService.ts
│   └── IMessageFormatter.ts
├── services/            # Implementaciones concretas (SRP)
│   ├── GreetingService.ts
│   └── MessageFormatter.ts
├── hooks/               # Lógica de negocio (SRP)
│   └── useHelloWorld.ts
├── components/          # Componentes UI (SRP, OCP)
│   └── HelloWorld/
│       ├── HelloWorldContainer.tsx
│       ├── HelloWorldPresentation.tsx
│       └── HelloWorld.css
└── App.tsx
```

## Beneficios de esta Arquitectura

1. **Mantenibilidad**: Cada componente tiene una responsabilidad clara
2. **Testabilidad**: Las dependencias se pueden mockear fácilmente
3. **Extensibilidad**: Se pueden agregar nuevas funcionalidades sin modificar código existente
4. **Reutilización**: Los servicios y hooks pueden ser reutilizados en otros componentes
5. **Flexibilidad**: Las implementaciones se pueden intercambiar fácilmente

## Ejemplo de Extensión

Para agregar un nuevo tipo de saludo, simplemente:

1. Crear una nueva clase que implemente `IGreetingService`
2. Inyectarla en el `HelloWorldContainer`
3. No se requiere modificar ningún otro código

```typescript
export class FormalGreetingService implements IGreetingService {
  getGreeting(): string {
    return "Buenos días, estimado usuario";
  }
}
```
