import React from 'react';
import { HelloWorldPresentation } from './HelloWorldPresentation';
import { useHelloWorld } from '../../hooks/useHelloWorld';
import { GreetingService } from '../../services/GreetingService';
import { MessageFormatter } from '../../services/MessageFormatter';

// Dependency Inversion Principle (DIP)
// Este componente depende de abstracciones (interfaces) no de implementaciones concretas
// Open/Closed Principle (OCP)
// Este componente está cerrado para modificación pero abierto para extensión
export const HelloWorldContainer: React.FC = () => {
  // Inyección de dependencias - podríamos usar un contenedor DI más sofisticado
  const greetingService = new GreetingService();
  const messageFormatter = new MessageFormatter();
  
  const { message, isLoading } = useHelloWorld(greetingService, messageFormatter);

  return (
    <HelloWorldPresentation 
      message={message} 
      isLoading={isLoading} 
    />
  );
};
