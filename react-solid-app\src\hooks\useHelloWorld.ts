import { useState, useEffect } from 'react';
import { IGreetingService } from '../interfaces/IGreetingService';
import { IMessageFormatter } from '../interfaces/IMessageFormatter';

// Single Responsibility Principle (SRP)
// Este hook tiene una sola responsabilidad: manejar la lógica del "Hola Mundo"
export const useHelloWorld = (
  greetingService: IGreetingService,
  messageFormatter: IMessageFormatter
) => {
  const [message, setMessage] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    // Simulamos una carga asíncrona
    const loadMessage = async () => {
      setIsLoading(true);
      
      // Simulamos un delay para mostrar el loading
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const greeting = greetingService.getGreeting();
      const formattedMessage = messageFormatter.format(greeting);
      
      setMessage(formattedMessage);
      setIsLoading(false);
    };

    loadMessage();
  }, [greetingService, messageFormatter]);

  return { message, isLoading };
};
